import type { paths } from './schema'

export type MoodTrustIndexTableQueryParams = NonNullable<
  paths['/nodiens/api/v1/mood-index/']['get']['parameters']['query']
>

export type CommunityAsset =
  paths['/nodiens/api/v1/community/asset']['get']['responses']['200']['content']['application/json']['payload']['data'][0]

export type MoodTrustIndexPayload =
  paths['/nodiens/api/v1/mood-index/']['get']['responses']['200']['content']['application/json']['payload']
