<script setup lang="ts">
import type { HLCAreaData } from '~/composables/custom-plugin/data'
import type { AssetType } from '~/types/appTypes'
import { format } from 'date-fns'
import { LineStyle, type Time, type WhitespaceData } from 'lightweight-charts'
import FileSaver from 'file-saver'
import jsPDF from 'jspdf'
import domtoimage from 'dom-to-image'

// Constants
import { CLIMATE_TOOLTIPS } from '~/constants/common-tooltips'
import { ESG_CARBON_EMISSION_COLOR_SOURCES, ESG_CHART_TYPE_OPTION_LABEL } from '~/constants/options'
import {
  CLIMATE_METRIC_SLUGS,
  CLIMATE_METRIC_KEYS,
  ENERGY_CONSUMPTION_TYPES,
  CARBON_EMISSION_TYPE_OPTIONS,
  CLIMATE_METRIC_OPTIONS,
} from '~/constants/climate'

// Helpers
import { displayWattage, displayWeight, numericDisplay } from '~/helper/number-helper'
import { formatToSlug } from '~/helper/string-helper'
import { blobToBase64 } from '~/helper/utilities-helper'

// Assets
import type { addLineDataType, addRangeAreaDataType } from '~/composables/types/light-chart'
import type { EsgAsset } from '~/types'

const { $client, $colorMode, $apiClient } = useNuxtApp()
const config = useRuntimeConfig()
const route = useRoute()
const router = useRouter()
const toast = useToast()
const { onBack } = useBackHandler()
const { isMobile } = useDeviceScreen()

const { isFeatureAllowed } = useFeature()

const isClimateFeatureAllowed = computed(
  () =>
    !!isFeatureAllowed('esg_climate_metrics.power_use') ||
    !!isFeatureAllowed('esg_climate_metrics.energy_cons_tx') ||
    !!isFeatureAllowed('esg_climate_metrics.energy_cons_tx_node'),
)

const {
  chart: chartInstance,
  zoomViews,
  log,
  tools,
  addLineSeries,
  addAreaRangeSeries,
  addAreaSeries,
  setPriceScaleVisibility,
  renderChart,
  renderBottomChart,
  destroyChart,
} = useLightChart()

const { blockchain, metric } = route.params as Record<string, string>

if (!metric || !CLIMATE_METRIC_KEYS[metric as keyof typeof CLIMATE_METRIC_KEYS] || !blockchain) {
  throw createError({ statusCode: 404, statusMessage: 'Page Not Found' })
}

const esgChartRef = ref<HTMLElement>()
const esgBottomChartRef = ref<HTMLElement>()
const exportElement = shallowRef<HTMLElement>()

/**
 * Helper number used to temp transfrom small values to larger values to display on lightweight chart
 * This approach is need to show small values, because apparently lightweight chart doesn't support small numbers
 */
const helperNumber = ref(100000)

const selectedType = ref<string>((route.query?.type as string) ?? 'powerUse')
const selectedCarbonMetric = ref<string>((route.query?.carbonMetric as string) ?? 'emission')

const selectedAsset = useState<{
  id: number | undefined
  name: string | undefined
  slug: string | null | undefined
  symbol: string | undefined
  logo: string | null | undefined
  type: string
  layer: string | null
}>()
const consensusMechanism = useState<(string | null)[]>(() => [])
const typeEsgLayer = useState(() => '')

const isShareModalOpen = ref(false)
const fullPath = computed(() => `${config.public.appUrl}/climate/${metric}/${blockchain}`)

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const searchAsset = async (q: string) => {
  let assets: EsgAsset[] = []

  if (metric === CLIMATE_METRIC_SLUGS.energyConsumption) {
    assets = await $apiClient
      .GET('/nodiens/api/v1/esg/energy-consumption/asset', {
        params: {
          query: {
            ...(q && { terms: q }),
          },
        },
      })
      .then((res) => res.data?.payload?.data ?? [])
  }

  if (metric === CLIMATE_METRIC_SLUGS.carbonEmission) {
    assets = await $apiClient
      .GET('/nodiens/api/v1/esg/carbon-emission/asset', {
        params: {
          query: {
            ...(q && { terms: q }),
          },
        },
      })
      .then((res) => res.data?.payload?.data ?? [])
  }

  return assets
}

const {
  data: chartData,
  status,
  refresh,
} = useLazyAsyncData(
  'getEsgChart',
  async () => {
    try {
      const api =
        metric === CLIMATE_METRIC_SLUGS.energyConsumption
          ? $client.v2.user.climate.energyConsumption
          : $client.v2.user.climate.carbonEmission

      const res = await api.chart.query({
        slug: blockchain,
        // @ts-expect-error ignore
        metric: metric === CLIMATE_METRIC_SLUGS.energyConsumption ? selectedType.value : selectedCarbonMetric.value,
      })

      selectedAsset.value = {
        id: res.feeds && 'id' in res.feeds ? res.feeds.id : undefined,
        type: res.feeds?.type ?? '',
        slug: res.feeds?.slug ?? '',
        layer: res.feeds?.layer ?? null,
        symbol: res.feeds?.symbol || '',
        name: res.feeds?.name || '',
        logo: res.feeds?.logo || '',
      }
      consensusMechanism.value = res?.group || []
      typeEsgLayer.value = res.feeds?.layer || ''

      return res
    } catch (error: any) {
      let message = 'Something went wrong'

      if (error?.message === 'ASSET_NOT_FOUND' || error?.data?.code === 'NOT_FOUND') {
        message = 'Asset not found'
      }

      toast.add({
        title: 'Potential Issue with Data',
        description: message,
        icon: 'i-heroicons-exclamation-triangle',
        color: 'amber',
      })

      setTimeout(() => {
        router.replace('/climate')
      }, 3000)

      return null
    }
  },
  {
    watch: [selectedType, selectedCarbonMetric],
    server: false,
    immediate: false,
  },
)

const entries = computed(() => {
  if (chartData.value && 'entries' in chartData.value && Array.isArray(chartData.value.entries.entries)) {
    return chartData.value.entries.entries
  }

  return []
})

const entriesMode = computed(() => {
  if (chartData.value && 'entries' in chartData.value && 'mode' in chartData.value.entries) {
    return chartData.value.entries.mode as 'source' | 'annualized'
  }

  return []
})

const dailyTimeseries = computed(() => {
  if (chartData.value && 'daily_timeseries' in chartData.value) {
    return chartData.value.daily_timeseries
  }

  return []
})

const metricTimeseries = computed(() => {
  if (chartData.value && 'metric_timeseries' in chartData.value) {
    return chartData.value.metric_timeseries
  }

  return []
})

const chartDataMetric = computed(() => {
  if (chartData.value && 'metric' in chartData.value) {
    return chartData.value.metric as 'emission' | 'source' | 'pertx' | 'pertxnode'
  }

  return ''
})

const isChartLoading = computed(() => status.value === 'pending')

const isChartEmpty = computed(() => {
  if (isChartLoading.value) {
    return false
  }
  if (metric === CLIMATE_METRIC_SLUGS.energyConsumption) {
    return dailyTimeseries.value.length === 0 && metricTimeseries.value.length === 0
  } else if (metric === CLIMATE_METRIC_SLUGS.carbonEmission) {
    return entries.value.length === 0
  }
  return false
})

const metricOptions = computed(() => {
  // @ts-ignore
  const metricData = chartData.value?.available_metric || chartData.value?.available_climate
  return CLIMATE_METRIC_OPTIONS.map((item) => ({
    ...item,
    disabled: !metricData?.[item.value.replace('-', '_') as keyof typeof metricData],
  }))
})

const smallestSelectedAvgUnit = computed(() => {
  if (!chartData.value) {
    return ''
  }

  if (metric === CLIMATE_METRIC_SLUGS.energyConsumption && !chartDataMetric.value) {
    const averageValues =
      metricTimeseries.value.map(([, value]) => (typeof value === 'object' && 'avg' in value ? value.avg : 0)) ?? []
    const min = Math.min(...averageValues)
    const postfix = selectedType.value === 'powerUse' ? '' : 'h'
    return displayWattage(min).split(' ')[1] + postfix
  } else if (metric === CLIMATE_METRIC_SLUGS.carbonEmission && chartDataMetric.value) {
    if (entries.value.length === 0) {
      return ''
    }

    const averageValues =
      entriesMode.value === 'annualized'
        ? (entries.value
            .filter(
              (
                entry,
              ): entry is [
                number,
                {
                  avg: number
                  top: number
                  lower: number
                  tps: number
                  validators: number
                },
              ] => Array.isArray(entry),
            )
            .map(([_, value]) => value.avg) ?? [])
        : (entries.value
            .filter((entry): entry is { type: string; timeseries: [number, number][] } => 'timeseries' in entry)
            .map((entry) => entry.timeseries.map(([_, value]) => value))
            .flat() ?? [])

    const min = Math.min(...averageValues)
    return displayWeight(min).split(' ')[1] + ' CO₂eq'
  }

  return ''
})

const dateRange = computed(() => {
  const result = { startDate: new Date(), latestDate: new Date() }

  let timestamps: number[] = []

  if (metric === CLIMATE_METRIC_SLUGS.energyConsumption && !chartDataMetric.value) {
    timestamps = [...(dailyTimeseries.value as [number, any][]).map(([time]) => time)]
  } else if (metric === CLIMATE_METRIC_SLUGS.carbonEmission && chartDataMetric.value) {
    timestamps = [
      ...(entriesMode.value === 'annualized'
        ? (entries.value
            .filter(
              (
                entry,
              ): entry is [number, { tps: number; validators: number; avg: number; lower: number; top: number }] =>
                Array.isArray(entry),
            )
            .map(([time]) => time) ?? [])
        : entries.value[0] && 'timeseries' in entries.value[0]
          ? entries.value[0].timeseries.map(([time]) => time)
          : []),
    ]
  }

  const uniqueTimestamps = [...new Set(timestamps)]
  if (uniqueTimestamps.length) {
    const minTs = Math.min(...uniqueTimestamps)
    const maxTs = Math.max(...uniqueTimestamps)
    result.startDate = new Date(minTs)
    result.latestDate = new Date(maxTs)
  }

  return result
})

const selectedMetric = computed({
  get: () => metric,
  set: async (value) => {
    await router.replace(`/climate/${value}/${blockchain}`)
  },
})

const selectedEmissionSources = computed(() => {
  if (
    chartData.value &&
    'entries' in chartData.value &&
    Array.isArray(chartData.value.entries) &&
    chartData.value.entries.length === 0
  ) {
    return []
  }

  const selectedEmissionSources =
    entries.value
      .filter((entry): entry is { type: string; timeseries: [number, number][] } => 'type' in entry)
      .map((entry) => entry.type) ?? []

  if (selectedEmissionSources.length === 0) {
    return []
  }

  const selectedEmissionSourcesList: { label: string; color: string }[] = selectedEmissionSources
    .filter((entry: string) => ESG_CARBON_EMISSION_COLOR_SOURCES[entry])
    .map((entry: string) => ({
      label: entry,
      color: ESG_CARBON_EMISSION_COLOR_SOURCES[entry]?.lineColor || '',
    }))

  return selectedEmissionSourcesList.map((entry) => ({
    label: entry,
    color: ESG_CARBON_EMISSION_COLOR_SOURCES[entry.label]?.lineColor,
  }))
})

const initializeChart = () => {
  if (chartInstance.value === undefined && esgChartRef.value) {
    renderChart(
      esgChartRef,
      {
        grid: {
          horzLines: {
            color: '#C3C3C3',
            style: LineStyle.Dashed,
          },
          vertLines: {
            visible: false,
          },
        },
        crosshair: {
          horzLine: {
            visible: false,
            labelVisible: false,
          },
        },
        leftPriceScale: {
          visible: isMobile.value ? false : true,
          borderVisible: false,
          entireTextOnly: true,
          textColor: '#C3C3C3',
          scaleMargins: {
            bottom: 0.01,
          },
        },
        rightPriceScale: {
          visible: isMobile.value ? false : true,
          borderVisible: false,
          entireTextOnly: true,
          textColor: '#C3C3C3',
          scaleMargins: {
            bottom: 0.01,
          },
        },
        timeScale: {
          visible: false,
          borderVisible: false,
          borderColor: 'rgba(197, 203, 206, 1)',
          fixRightEdge: true,
          rightOffset: 5,
          lockVisibleTimeRangeOnResize: true,
        },
        handleScale: {
          pinch: false,
          mouseWheel: false,
          axisPressedMouseMove: false,
        },
        handleScroll: {
          mouseWheel: false,
          vertTouchDrag: false,
          horzTouchDrag: false,
          pressedMouseMove: false,
        },
        startCalculation: format(dateRange.value.startDate, 'yyyy-MM-dd'),
        latestCalculation: format(dateRange.value.latestDate, 'yyyy-MM-dd'),
      },
      darkMode.value,
    )
  }

  if (chartInstance.value && chartData.value) {
    if (metric === CLIMATE_METRIC_SLUGS.energyConsumption && !chartDataMetric.value) {
      // Add Average line series
      const avgSeries = addLineSeries('avg', {
        priceScaleId: 'left',
        color: '#4FBD6D',
        lineWidth: 2,
        crosshairMarkerVisible: false,
        lastValueVisible: false,
        priceLineVisible: false,
        priceFormat: {
          type: 'custom',
          formatter: (price: number) => {
            return metric === CLIMATE_METRIC_SLUGS.energyConsumption
              ? displayWattage(price / helperNumber.value).split(' ')[0]
              : displayWeight(price / helperNumber.value).split(' ')[0]
          },
        },
      })

      const avgSeriesData = metricTimeseries.value.map((item: any) => {
        const [time, value] = item
        return {
          time: format(time, 'yyyy-MM-dd') as Time,
          value: value.avg * helperNumber.value,
          customValues: {
            name: 'Best Guess',
          },
        }
      })

      avgSeries?.setData(avgSeriesData)

      // Add Range series
      const rangeSeries = addAreaRangeSeries('range', {
        priceScaleId: 'left',
        lastValueVisible: false,
        priceLineVisible: false,
        closeLineWidth: 1,
        highLineWidth: 1,
        lowLineWidth: 1,
        areaTopColor: 'rgba(79, 189, 109, 0.15)',
        areaBottomColor: 'rgba(79, 189, 109, 0.15)',
        color: 'rgba(79, 189, 109, 0.15)',
        closeLineColor: 'rgba(255, 255, 255, 0)',
        highLineColor: 'rgba(79, 189, 109, 0)',
        lowLineColor: 'rgba(79, 189, 109, 0)',
        priceFormat: {
          type: 'custom',
          formatter: (price: number) => {
            return metric === CLIMATE_METRIC_SLUGS.energyConsumption
              ? displayWattage(price / helperNumber.value)
              : displayWeight(price / helperNumber.value)
          },
        },
      })

      const rangeSeriesData: (HLCAreaData | WhitespaceData)[] = metricTimeseries.value.map((item: any) => {
        const [time, value] = item
        let avg = value.avg

        if (avg < value.lower || avg > value.upper) {
          avg = value.lower
        }

        return {
          time: format(time, 'yyyy-MM-dd') as Time,
          low: value.lower * helperNumber.value,
          high: value.upper * helperNumber.value,
          close: avg * helperNumber.value,
          customValues: {
            name: 'Range',
          },
        }
      })

      rangeSeries?.setData(rangeSeriesData)

      // Add TPS line series
      const tpsSeries = addLineSeries('tps', {
        priceScaleId: 'right',
        color: '#ff0000',
        lineWidth: 2,
        crosshairMarkerVisible: false,
        lastValueVisible: false,
        priceLineVisible: false,
        priceFormat: {
          type: 'custom',
          formatter: (price: number) => {
            return numericDisplay(price / helperNumber.value)
          },
        },
      })

      const tpsSeriesData = dailyTimeseries.value.map((item: any) => {
        const [time, value] = item
        return {
          time: format(time, 'yyyy-MM-dd') as Time,
          value: value.tps * helperNumber.value,
          customValues: {
            name: 'Throughput',
          },
        }
      })

      tpsSeries?.setData(tpsSeriesData)
    } else if (metric === CLIMATE_METRIC_SLUGS.carbonEmission && chartDataMetric.value) {
      // Add Average line series
      const avgSeries: addLineDataType[] = []

      if (entriesMode.value === 'annualized') {
        const series =
          avgSeries.push(
            addLineSeries(ESG_CHART_TYPE_OPTION_LABEL[selectedType.value] || '', {
              priceScaleId: 'left',
              color: '#4FBD6D',
              lineWidth: 2,
              crosshairMarkerVisible: false,
              lastValueVisible: false,
              priceLineVisible: false,
              priceFormat: {
                type: 'custom',
                formatter: (price: number) => {
                  return metric === (CLIMATE_METRIC_SLUGS.energyConsumption as string)
                    ? displayWattage(price / helperNumber.value).split(' ')[0]
                    : displayWeight(price / helperNumber.value).split(' ')[0]
                },
              },
            }),
          ) - 1

        avgSeries[series]?.setData(
          entries.value.map((item: any) => {
            const [time, value] = item
            return {
              time: format(time, 'yyyy-MM-dd') as Time,
              value: value.avg * helperNumber.value,
              customValues: {
                name: 'Best Guess',
              },
            }
          }),
        )
      }
      if (entriesMode.value === 'source') {
        for (const entry of entries.value) {
          if ('type' in entry) {
            const series = addAreaSeries(entry.type, {
              lineColor: ESG_CARBON_EMISSION_COLOR_SOURCES[entry.type]?.lineColor ?? 'rgba(136, 48, 57, 1)',
              priceLineColor: ESG_CARBON_EMISSION_COLOR_SOURCES[entry.type]?.lineColor ?? 'rgba(136, 48, 57, 1)',
              topColor: ESG_CARBON_EMISSION_COLOR_SOURCES[entry.type]?.lineColor ?? 'rgba(136, 48, 57, 1)',
              bottomColor: ESG_CARBON_EMISSION_COLOR_SOURCES[entry.type]?.bgColor ?? 'rgba(136, 48, 57, 0.5)',
              priceScaleId: 'left',
              lineWidth: 2,
              crosshairMarkerVisible: false,
              lastValueVisible: false,
              priceLineVisible: false,
              priceFormat: {
                type: 'custom',
                formatter: (price: number) => {
                  return metric === ('energy-consumption' as string)
                    ? displayWattage(price / helperNumber.value).split(' ')[0]
                    : displayWeight(price / helperNumber.value).split(' ')[0]
                },
              },
            })
            if ('timeseries' in entry && Array.isArray(entry.timeseries)) {
              series?.setData(
                entry.timeseries.map((item) => {
                  const [time, value] = item
                  return {
                    time: format(time, 'yyyy-MM-dd') as Time,
                    value: value * helperNumber.value,
                    customValues: {
                      name: entry.type,
                    },
                  }
                }),
              )
            }
          }
        }
      }

      const rangeSeries: addRangeAreaDataType[] = []

      if (entriesMode.value === 'annualized') {
        // Add Range series
        const rangeSerieIndex =
          rangeSeries.push(
            addAreaRangeSeries('range', {
              priceScaleId: 'left',
              lastValueVisible: false,
              priceLineVisible: false,
              closeLineWidth: 1,
              highLineWidth: 1,
              lowLineWidth: 1,
              areaTopColor: 'rgba(79, 189, 109, 0.15)',
              areaBottomColor: 'rgba(79, 189, 109, 0.15)',
              color: 'rgba(79, 189, 109, 0.15)',
              closeLineColor: 'rgba(255, 255, 255, 0)',
              highLineColor: 'rgba(79, 189, 109, 0)',
              lowLineColor: 'rgba(79, 189, 109, 0)',
              priceFormat: {
                type: 'custom',
                formatter: (price: number) => {
                  return metric === (CLIMATE_METRIC_SLUGS.energyConsumption as string)
                    ? displayWattage(price / helperNumber.value)
                    : displayWeight(price / helperNumber.value)
                },
              },
            }),
          ) - 1

        rangeSeries[rangeSerieIndex]?.setData(
          entries.value.map((item: any) => {
            const [time, value] = item
            let avg = value.avg

            if (avg < value.lower || avg > value.top) {
              avg = value.lower
            }

            return {
              time: format(time, 'yyyy-MM-dd') as Time,
              low: value.lower * helperNumber.value,
              high: value.top * helperNumber.value,
              close: avg * helperNumber.value,
              customValues: {
                name: 'Range',
              },
            }
          }),
        )
      }

      if (entriesMode.value === 'annualized') {
        // Add TPS line series
        const tpsSeries = addLineSeries('tps', {
          priceScaleId: 'right',
          color: '#ff0000',
          lineWidth: 2,
          crosshairMarkerVisible: false,
          lastValueVisible: false,
          priceLineVisible: false,
          priceFormat: {
            type: 'custom',
            formatter: (price: number) => {
              return numericDisplay(price / helperNumber.value)
            },
          },
        })
        const tpsSeriesData = entries.value.map((item: any) => {
          const [time, value] = entriesMode.value === 'annualized' ? item : item.timeseries
          return {
            time: format(entriesMode.value === 'annualized' ? time : time[0], 'yyyy-MM-dd') as Time,
            value: value.tps * helperNumber.value,
            customValues: {
              name: 'Throughput',
            },
          }
        })
        tpsSeries?.setData(tpsSeriesData)
      }
    }

    tools.reArrangeChart({ autosize: true })
    tools.prepareTooltip({
      topOffset: -30,
      darkMode: darkMode.value,
      valueFormatter: (value, seriesKey) => {
        const unit = selectedType.value === 'powerUse' ? '' : 'h'

        return seriesKey === 'tps'
          ? `${numericDisplay(value / helperNumber.value)} tps`
          : metric === CLIMATE_METRIC_SLUGS.energyConsumption
            ? displayWattage(value / helperNumber.value) + unit
            : displayWeight(value / helperNumber.value)
      },
      customLineCallback: (pointX) => {
        let value

        if (metric === CLIMATE_METRIC_SLUGS.energyConsumption && !chartDataMetric.value) {
          value = (dailyTimeseries.value[pointX]?.[1] as { validator: number })?.validator ?? ''
        } else if (metric === CLIMATE_METRIC_SLUGS.carbonEmission && chartDataMetric.value) {
          const entry = entries.value?.[pointX]
          if (Array.isArray(entry) && typeof entry[1] === 'object' && 'validators' in entry[1]) {
            value = entry[1].validators
          } else {
            value = ''
          }
        }

        return {
          unit: '#',
          label: 'Validators',
          value: value ? numericDisplay(value, 0) : '',
        }
      },
    })
    renderBottomChart(esgBottomChartRef, '#range-slider', darkMode.value)
  }
}
const esgChartTypeOptionLabel = computed(
  () =>
    ESG_CHART_TYPE_OPTION_LABEL[
      metric === CLIMATE_METRIC_SLUGS.energyConsumption ? selectedType.value : selectedCarbonMetric.value
    ],
)

const printContent = (target: string, blob?: Blob) => {
  const fileName = `${blockchain}_${formatToSlug(esgChartTypeOptionLabel.value || 'default')}_${format(new Date(), 'yyyyMMddHHmm')}_(Nodiens)`
  if (target !== 'pdf') {
    FileSaver.saveAs(blob!, `${fileName}.${target}`)
  } else {
    blobToBase64(blob!, (str) => {
      const pdf = new jsPDF({
        orientation: 'landscape',
      })
      pdf.addImage(str, 'PNG', 10, 10, 280, 110)
      pdf.save(`${fileName}.${target}`)
    })
  }
}

const print = (target: 'png' | 'jpg' | 'pdf') => {
  let previousValue: { from: Time; to: Time } | undefined
  if (chartInstance.value) {
    const opt = chartInstance.value.timeScale().getVisibleRange()
    if (opt) {
      previousValue = {
        from: opt.from,
        to: opt.to,
      }
    }
    chartInstance.value.timeScale().applyOptions({
      visible: true,
    })
    chartInstance.value.applyOptions({
      width: 1584,
      height: 534,
    })
  }
  const canvas = chartInstance.value?.takeScreenshot()
  if (chartInstance.value) {
    chartInstance.value.timeScale().applyOptions({
      visible: false,
    })
    chartInstance.value.applyOptions({
      width: esgChartRef.value?.offsetWidth,
      height: esgChartRef.value?.offsetHeight,
    })
    if (previousValue) {
      chartInstance.value.timeScale().setVisibleRange({
        from: previousValue.from,
        to: previousValue.to,
      })
    }
  }
  if (canvas && exportElement.value) {
    canvas.style.maxWidth = '100%'
    exportElement.value!.style.display = 'block'
    exportElement.value?.appendChild(canvas)

    const chartWatermark = document.getElementById('chart-watermark')
    if (chartWatermark) {
      const img = chartWatermark.querySelector('img')
      if (img) {
        img.style.left = `${canvas.offsetWidth / 4}px`
        img.style.width = `${canvas.offsetWidth / 2}px`
        img.style.top = `${canvas.offsetHeight / 3.5}px`
      }
    }

    const body = document.querySelector('body')

    if (body) {
      body.style.overflow = 'hidden'
      domtoimage
        .toBlob(exportElement.value)
        .then((blob: any) => {
          printContent(target, blob)
        })
        .finally(() => {
          body.style.overflow = 'unset'
          exportElement.value!.style.display = 'none'
          const canvas = exportElement.value?.querySelector('canvas')
          if (canvas) {
            canvas.remove()
          }
        })
    }
  }
}

const handleAssetChange = (asset: AssetType) => {
  router.push(`/climate/${metric}/${asset?.slug}`)
}

watch(isMobile, (value) => {
  if (value) {
    setPriceScaleVisibility('left', false)
    setPriceScaleVisibility('right', false)
  } else {
    setPriceScaleVisibility('left', true)
    setPriceScaleVisibility('right', true)
  }
})

watch(darkMode, (value) => {
  tools.setDark(value)
})

watch([selectedType, selectedCarbonMetric], () => {
  if (chartInstance.value) {
    destroyChart()
  }
  router.replace({
    query: {
      ...(metric === CLIMATE_METRIC_SLUGS.energyConsumption
        ? { type: selectedType.value }
        : metric === CLIMATE_METRIC_SLUGS.carbonEmission
          ? { carbonMetric: selectedCarbonMetric.value }
          : {}),
    },
  })
})

watch(esgChartRef, (element) => {
  if (element !== undefined) {
    setTimeout(() => initializeChart(), 50)
  }
})

onUnmounted(() => {
  destroyChart()
})

onMounted(() => {
  refresh()
})
</script>

<template>
  <div>
    <Head>
      <Title>{{ selectedAsset?.name }} Climate Indexes</Title>
    </Head>
    <HeaderPage page-name="climate-indices" />
    <div class="md:px-5 md:py-6">
      <div class="mb-4 hidden justify-between gap-2 md:mb-6 md:flex">
        <UButton
          class="inline-flex"
          variant="outline"
          color="black"
          size="md"
          icon="i-heroicons-arrow-left"
          label="Back"
          @click="onBack"
        />

        <div class="flex gap-2">
          <div class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white">
            <p class="text-neutrals-400 text-base dark:text-white">
              {{ typeEsgLayer }}
            </p>
            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="CLIMATE_TOOLTIPS.esgLayer" />
              </TooltipContent>
            </InformationPopover>
          </div>
          <div
            v-for="item in consensusMechanism"
            :key="(item as string).replace(' ', '-')"
            class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white"
          >
            <p class="text-neutrals-400 text-base dark:text-white">
              {{ item }}
            </p>
            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="CLIMATE_TOOLTIPS.consensusMechanism" />
              </TooltipContent>
            </InformationPopover>
          </div>
        </div>
      </div>

      <UCard>
        <div class="flex flex-col gap-4">
          <div class="flex flex-col gap-4">
            <div class="flex gap-2 self-end md:hidden">
              <div class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white">
                <p class="text-neutrals-400 text-base dark:text-white">
                  {{ typeEsgLayer }}
                </p>
                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p v-html="CLIMATE_TOOLTIPS.esgLayer" />
                  </TooltipContent>
                </InformationPopover>
              </div>
              <div
                v-for="item in consensusMechanism"
                :key="(item as string).replace(' ', '_')"
                class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white"
              >
                <p class="text-neutrals-400 text-base dark:text-white">
                  {{ item }}
                </p>
                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p v-html="CLIMATE_TOOLTIPS.consensusMechanism" />
                  </TooltipContent>
                </InformationPopover>
              </div>
            </div>
            <div class="flex flex-col flex-wrap gap-4 md:flex-row md:justify-between">
              <div>
                <div
                  v-if="isChartLoading"
                  class="flex gap-2"
                >
                  <USkeleton class="h-10 flex-1 md:w-44" />
                  <USkeleton class="h-10 flex-1 md:w-44" />
                  <USkeleton class="h-10 flex-1 md:w-44" />
                </div>
                <div
                  v-else
                  class="flex flex-col gap-4 md:flex-row"
                >
                  <AssetSelector
                    v-model="selectedAsset"
                    :search-assets="searchAsset"
                    @change="handleAssetChange"
                  />

                  <USelectMenu
                    v-model="selectedMetric"
                    :options="metricOptions"
                    :ui-menu="{
                      container: 'w-min',
                      label: 'text-base',
                    }"
                    value-attribute="value"
                    variant="solid"
                    :popper="{ placement: 'bottom-start' }"
                    size="md"
                    color="black"
                    class="w-full md:w-fit"
                  />

                  <USelectMenu
                    v-if="metric === CLIMATE_METRIC_SLUGS.energyConsumption"
                    v-model="selectedType"
                    :options="ENERGY_CONSUMPTION_TYPES"
                    :ui-menu="{
                      container: 'w-min',
                      label: 'text-base',
                    }"
                    variant="solid"
                    :popper="{ placement: 'bottom-start' }"
                    size="md"
                    color="black"
                    class="w-full md:w-fit"
                  >
                    <template #label>
                      <span class="text-base">{{ ESG_CHART_TYPE_OPTION_LABEL[selectedType] }}</span>
                    </template>
                    <template #option="{ option: type }">
                      <span class="whitespace-nowrap">
                        {{ ESG_CHART_TYPE_OPTION_LABEL[type] }}
                      </span>
                    </template>
                  </USelectMenu>

                  <USelectMenu
                    v-else-if="metric === CLIMATE_METRIC_SLUGS.carbonEmission"
                    v-model="selectedCarbonMetric"
                    :options="CARBON_EMISSION_TYPE_OPTIONS"
                    value-attribute="value"
                    :ui-menu="{
                      container: 'w-min',
                      label: 'text-base',
                    }"
                    variant="solid"
                    color="black"
                    :popper="{ placement: 'bottom-start' }"
                    size="md"
                    class="w-full md:w-fit"
                  />
                </div>
              </div>

              <div class="md:ml-auto lg:ml-0">
                <div
                  v-if="isChartLoading"
                  class="flex items-center gap-x-2"
                >
                  <USkeleton
                    v-for="item in 6"
                    :key="item"
                    class="h-9 w-12"
                  />
                </div>
                <div
                  v-else
                  class="flex items-center justify-between gap-1.5 md:gap-2"
                >
                  <UButton
                    v-for="zoom in zoomViews"
                    :key="zoom.id"
                    :variant="zoom.active ? 'outline' : 'ghost'"
                    :color="zoom.active ? 'brand' : 'black'"
                    :class="[
                      'px-1.5 !font-normal sm:px-2.5',
                      { 'dark:!text-neutrals-50 !text-black/50': !zoom.active },
                    ]"
                    @click="tools.setZoom(zoom.id)"
                  >
                    {{ zoom.label }}
                  </UButton>
                  <UButton
                    size="md"
                    icon="i-material-symbols-share"
                    color="soft-gray"
                    variant="outline"
                    @click="isShareModalOpen = true"
                  />
                </div>
              </div>
            </div>
            <div class="flex justify-between">
              <div>
                <div
                  v-if="isChartLoading"
                  class="flex gap-2"
                >
                  <USkeleton class="h-10 w-44" />
                  <USkeleton class="h-10 w-16" />
                </div>
                <div
                  v-else-if="selectedEmissionSources.length"
                  class="flex flex-wrap items-center gap-3"
                >
                  <div
                    v-for="item in selectedEmissionSources"
                    :key="String(item.label)"
                    class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white"
                  >
                    <div
                      :style="{ backgroundColor: item.color }"
                      class="h-2 w-2 rounded-full"
                    />
                    <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{ item.label }}</span>
                  </div>
                </div>
                <div
                  v-else
                  class="flex items-center gap-3"
                >
                  <div class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white">
                    <div class="bg-accent-green-400 h-2 w-2 rounded-full" />
                    <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{
                      esgChartTypeOptionLabel
                    }}</span>
                  </div>
                  <div class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white">
                    <div class="bg-accent-red-700 h-2 w-2 rounded-full" />
                    <span class="text-neutrals-300 ml-2 text-base dark:text-white">Throughput</span>
                  </div>
                </div>
              </div>

              <div class="hidden md:block">
                <div
                  v-if="isChartLoading"
                  class="flex items-center gap-x-2"
                >
                  <USkeleton
                    v-for="item in 2"
                    :key="item"
                    class="h-9 w-12"
                  />
                </div>

                <div
                  v-else
                  class="flex items-center gap-x-2 self-end"
                >
                  <UButton
                    size="md"
                    variant="outline"
                    :color="log ? 'brand' : 'soft-gray'"
                    class="!font-normal"
                    @click="tools.handleLog"
                  >
                    LOG
                  </UButton>
                  <UPopover>
                    <UButton
                      size="md"
                      color="soft-gray"
                      variant="outline"
                      icon="i-heroicons-arrow-down-tray"
                    />

                    <template #panel="{ close }">
                      <div class="flex flex-col p-2">
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              print('pdf')
                            }
                          "
                        >
                          Export as PDF
                        </UButton>
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              print('png')
                            }
                          "
                        >
                          Export as PNG
                        </UButton>
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              print('jpg')
                            }
                          "
                        >
                          Export as JPG
                        </UButton>
                      </div>
                    </template>
                  </UPopover>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="isChartLoading"
            class="v1-4xl:h-[600px] grid h-[400px] w-full place-items-center xl:h-[500px]"
          >
            <LottieNodiensLoader />
          </div>
          <CheckFeature
            v-else
            :allowed="isClimateFeatureAllowed"
          >
            <NoDataAvailable
              v-if="isChartEmpty"
              class="v1-4xl:h-[600px] h-[400px] xl:h-[500px]"
            />
            <div v-else>
              <div
                v-if="!isMobile"
                class="flex w-full items-center justify-between px-7 py-1"
              >
                <p class="text-neutrals-300 text-right text-xs font-medium">
                  {{ smallestSelectedAvgUnit }}
                </p>
                <p
                  v-if="!selectedEmissionSources.length"
                  class="text-neutrals-300 text-xs font-medium"
                >
                  tps
                </p>
              </div>
              <ClientOnly>
                <div
                  ref="esgChartRef"
                  class="v1-4xl:h-[600px] h-[400px] w-full xl:h-[500px]"
                />
              </ClientOnly>
              <div class="relative my-2 h-fit">
                <div
                  id="range-slider"
                  class="block bg-transparent"
                />
                <div
                  class="absolute top-0 flex h-[50px] w-full justify-center overflow-hidden"
                  style="z-index: 0"
                >
                  <div
                    ref="esgBottomChartRef"
                    class="h-[50px] w-full overflow-hidden rounded border border-neutral-400"
                    style="max-width: 98.6607%"
                  />
                </div>
              </div>
            </div>
          </CheckFeature>
        </div>
      </UCard>
    </div>

    <div
      ref="exportElement"
      class="min-h-[693px] w-[1648px] bg-white p-8 dark:bg-black"
      style="display: none"
    >
      <div class="flex justify-between gap-2">
        <div class="flex flex-col gap-2">
          <div class="flex items-center gap-2 font-normal">
            <img
              :src="
                chartData?.feeds?.logo ? '/api/v2/prx-image?url=' + chartData?.feeds?.logo + '&responseType=blob' : ''
              "
              alt="logo"
              class="mr-3 h-6 w-6"
            />
            <div class="text-base">
              {{ chartData?.feeds?.name ?? '-' }}
              <span class="text-neutrals-300">({{ chartData?.feeds?.symbol?.toUpperCase() ?? '-' }})</span>
            </div>
          </div>
          <div>Downloaded from Nodiens, {{ format(new Date(), 'MMM dd, yyyy kk:mm zzz') }}</div>
        </div>

        <div
          v-if="selectedEmissionSources.length"
          class="flex flex-wrap items-center gap-3"
        >
          <div
            v-for="item in selectedEmissionSources"
            :key="String(item.label)"
            class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white"
          >
            <div
              :style="{ backgroundColor: item.color }"
              class="h-2 w-2 rounded-full"
            />
            <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{ item.label }}</span>
          </div>
        </div>

        <div
          v-else
          class="flex items-center gap-3"
        >
          <div class="border-neutrals flex items-center rounded border px-2 py-[6px]">
            <div class="bg-accent-green-400 h-2 w-2 rounded-full" />
            <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{
              esgChartTypeOptionLabel
            }}</span>
          </div>
          <div class="border-neutrals-300 flex items-center rounded border px-2 py-[6px]">
            <div class="bg-accent-red-700 h-2 w-2 rounded-full" />
            <span class="text-neutrals-300 ml-2 text-base dark:text-white">Throughput</span>
          </div>
        </div>
      </div>

      <div class="mb-1 mt-6 flex w-full items-center justify-between px-7">
        <p class="text-neutrals-300 text-right text-xs font-medium">
          {{ smallestSelectedAvgUnit }}
        </p>
        <p
          v-if="!selectedEmissionSources.length"
          class="text-neutrals-300 text-xs font-medium"
        >
          tps
        </p>
      </div>

      <div
        id="chart-watermark"
        class="relative w-full"
      >
        <ColorScheme>
          <img
            v-if="darkMode"
            src="~/assets/media/chart-watermark-night.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
          <img
            v-else
            src="~/assets/media/chart-watermark.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
        </ColorScheme>
      </div>
    </div>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="fullPath"
      @on-close="isShareModalOpen = false"
    />
  </div>
</template>

<style>
#range-slider {
  margin: auto;
  width: 100%;
  height: 50px;
  background: transparent;
  overflow: hidden;
}
#range-slider .range-slider__thumb:nth-child(3) {
  transform: translate(0%, -50%) !important;
}
#range-slider .range-slider__thumb:nth-child(4) {
  transform: translate(-100%, -50%) !important;
}
.dark #range-slider .range-slider__thumb {
  border: 1px solid#e5e5e5;
}
#range-slider .range-slider__thumb {
  width: 14px;
  height: 100%;
  border-radius: 4px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='%23333' viewBox='0 0 24 24'%3E%3Cpath d='M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z' /%3E%3C/svg%3E")
    #fff;
  border: 1px solid #c3c3c3;
  background-repeat: no-repeat;
  background-position: center;
}
.dark #range-slider .range-slider__range {
  border: 1px solid #d9d9d9;
}
#range-slider .range-slider__range {
  border-radius: 6px;
  background: rgba(0, 163, 255, 0.1);
  border: 1px solid #c3c3c3;
  box-sizing: border-box;
}
</style>
